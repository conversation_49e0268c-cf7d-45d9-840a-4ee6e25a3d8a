import PdfPrinter from 'pdfmake';
import { Content, TDocumentDefinitions } from 'pdfmake/interfaces';
import { fileToSavePath, fonts, RsSvg, RsSvgPlaceholder } from '../constants';
import fs from 'fs';
import { isArabic } from '../utils/regexUtils';

// reverse sentence text
function reverseArabicText(text: string): string {
    return ` ${text}`.split(' ').reverse().join(' ');
}

// reverse arabic text ## the pdfmake library does not support RTL text
function handleTextDirectionForArabic(content: Content[] | Content): Content[] {
    const reverseTextIfArabic = (text: string) => {
        if (isArabic(text)) {
            return reverseArabicText(text);
        }
        return text;
    };

    const processText = (c: any) => {
        if (c.text) {
            c.text = reverseTextIfArabic(c.text);
        } else if (c.columns) {
            c.columns = c.columns.map((col: any) =>
                col.text ? { ...col, text: reverseTextIfArabic(col.text) } : col,
            );
        } else if (c.table) {
            c.table.body = c.table.body.map((row: any) =>
                row.map((col: any) => {
                    if (col.text) {
                        return { ...col, text: reverseTextIfArabic(col.text) };
                    } else if (Array.isArray(col)) {
                        return col.map((subCol: any) =>
                            subCol?.text
                                ? { ...subCol, text: reverseTextIfArabic(subCol?.text) }
                                : subCol,
                        );
                    } else {
                        return col
                    }
                }),
            );
        }
        return c;
    };

    if (Array.isArray(content)) return content.map(processText);
    else return [processText(content)];
}

// handle Doc content and options
function docDefinitionHelper(
    content: Content[] | Content,
): TDocumentDefinitions {
    // reverse all arabic text in content
    content = handleTextDirectionForArabic(content);

    return {
        pageSize: { height: 'auto', width: 200 },
        defaultStyle: {
            alignment: 'center',
            color: '#000',
            fontSize: 9,
            bold: true,
        },
        pageOrientation: 'portrait',
        pageMargins: [0, 0, 5, 0],
        language: 'ar',
        content,
    };
}

// save the doc to a file
async function handleSaveDocHelper(
    doc: TDocumentDefinitions,
    filePath: string,
) {
    var printer = new PdfPrinter(fonts);
    var pdfDoc = printer.createPdfKitDocument(doc);

    pdfDoc.pipe(fs.createWriteStream(filePath));
    pdfDoc.end();
    // ensure that the pdf is written before printing
    await new Promise((resolve) => setTimeout(resolve, 200));
}

export async function savePDF(
    content: Content[] | Content,
    filePath: string = fileToSavePath,
) {
    try {
        const doc = docDefinitionHelper(content);
        await handleSaveDocHelper(doc, filePath);
    } catch (error) {
        console.error(error);
        throw error;
    }
}

function replaceSvgPlaceholders(obj: any, placeholder: string, actualSvg: string): any {
    if (Array.isArray(obj)) {
        return obj.map(item => replaceSvgPlaceholders(item, placeholder, actualSvg));
    }

    if (typeof obj === "object" && obj !== null) {
        const newObj: any = {};
        for (const key in obj) {
            if (key === "svg" && obj[key] === placeholder) {
                newObj[key] = actualSvg;
            } else {
                newObj[key] = replaceSvgPlaceholders(obj[key], placeholder, actualSvg);
            }
        }
        return newObj;
    }

    return obj;
}

export const handleRsSvgPrint = (content: Content[] | Content): Content[] => {
    return replaceSvgPlaceholders(content, RsSvgPlaceholder, RsSvg);
}; 