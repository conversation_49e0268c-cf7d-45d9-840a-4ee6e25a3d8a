// pdfmake fonts
export var fonts = {
    Roboto: {
        normal: 'fonts/Tajawal-Regular.ttf',
        bold: 'fonts/Tajawal-Bold.ttf',
        italics: 'fonts/Tajawal-Light.ttf',
        bolditalics: 'fonts/Tajawal-ExtraLight.ttf',
    },
};

// Path: src/printers.ts
export const fileToSavePath = 'uploads/fileToPrint.pdf';

export const RsSvgPlaceholder = "RS_SVG_PLACEHOLDER";

export const RsSvg = `<svg version="1.0" xmlns="http://www.w3.org/2000/svg"
    width="1024.000000pt" height="1024.000000pt" viewBox="0 0 1024.000000 1024.000000"
    preserveAspectRatio="xMidYMid meet">
<metadata>
Created by potrace 1.16, written by <PERSON> 2001-2019
</metadata>
<g transform="translate(0.000000,1024.000000) scale(0.100000,-0.100000)"
fill="#000000" stroke="none">
<path d="M4645 9939 c-99 -77 -207 -162 -240 -188 -33 -26 -176 -139 -319
-252 -142 -112 -267 -210 -277 -218 -19 -13 -19 -60 -19 -2013 0 -1590 -3
-2000 -12 -2003 -7 -3 -380 -77 -828 -164 -448 -88 -963 -189 -1145 -224 -181
-36 -410 -80 -509 -98 -152 -29 -180 -37 -187 -54 -19 -43 -245 -1073 -237
-1080 2 -3 182 31 399 74 456 92 2150 428 2349 466 74 15 143 28 153 31 16 5
17 -31 17 -658 0 -634 -1 -665 -20 -706 -24 -54 -71 -98 -137 -128 -48 -23
-855 -188 -2571 -524 -314 -62 -425 -87 -432 -99 -7 -14 -222 -971 -237 -1057
-5 -30 -4 -31 23 -27 16 3 227 46 469 95 242 49 505 102 585 118 167 33 2024
408 2178 439 122 26 176 43 257 84 169 85 297 207 506 487 235 314 352 491
391 595 23 60 23 61 28 815 l5 754 535 107 535 106 3 -1063 c2 -895 4 -1064
16 -1064 7 0 274 52 592 115 659 130 1993 393 2344 462 129 25 239 49 243 53
5 5 27 93 48 196 22 104 41 196 44 204 3 8 34 157 70 330 36 173 69 328 72
343 5 25 4 28 -18 23 -13 -3 -166 -33 -339 -66 -173 -34 -475 -92 -670 -130
-195 -38 -569 -110 -830 -160 -261 -50 -483 -93 -492 -96 -17 -5 -18 24 -18
523 l0 529 273 53 c149 29 540 107 867 172 327 66 785 158 1018 204 233 47
427 87 431 90 5 2 14 33 20 67 7 35 60 276 117 536 58 261 103 476 101 478 -3
3 -110 -15 -238 -40 -129 -25 -364 -70 -524 -100 -159 -30 -384 -73 -500 -96
-284 -56 -469 -92 -725 -140 -118 -22 -354 -68 -524 -101 -169 -33 -310 -58
-312 -56 -2 2 -7 873 -11 1935 -3 1063 -8 1932 -11 1932 -5 0 -201 -163 -307
-256 -38 -33 -180 -153 -315 -265 -135 -112 -283 -236 -330 -277 l-85 -73 -5
-1602 -5 -1602 -115 -23 c-340 -67 -940 -182 -951 -182 -9 0 -10 496 -5 2305
5 1268 4 2305 -1 2305 -4 0 -89 -63 -188 -141z"/>
<path d="M8380 1885 c-1811 -361 -2105 -422 -2146 -441 -56 -26 -154 -129
-191 -199 -17 -33 -42 -94 -56 -135 -28 -80 -180 -790 -172 -798 3 -3 40 3 82
12 113 25 514 107 1408 286 1722 346 1890 381 1901 395 5 7 33 118 62 247 28
128 81 366 117 528 36 162 64 300 63 306 -2 8 -351 -58 -1068 -201z"/>
</g>
</svg>
`;
