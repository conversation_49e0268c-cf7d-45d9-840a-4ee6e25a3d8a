import { dirname, join } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { isPkg } from '../utils/global-utils';

export class SystemHelper {
    static getPath(options?: { path?: string; createIfNotExists?: boolean }) {
        const { path = '', createIfNotExists = true } = options || {};

        const  baseDir= isPkg ? dirname(process.execPath) : process.cwd();
        const dir = join(baseDir, path);
        if (createIfNotExists && !existsSync(dir)) mkdirSync(dir, { recursive: true });

        return dir;
    }
}
