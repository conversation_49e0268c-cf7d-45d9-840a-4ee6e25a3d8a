import { Content } from 'pdfmake/interfaces';
import { formatTime } from '../utils/dateUtils';
import {
    optionalDocItem,
    printerSingleDivider,
    textWithBorder,
} from '../utils/printerUtils';
import { IKitchenModel } from '../models/kitchenModel';

export const handleKitchenReceiptContent = (
    invoice: IKitchenModel,
): Content[] => {
    return [
        {
            image: './images/logo.jpg',
            width: 150,
            height: 100,
        },
        ...optionalDocItem(!!invoice.organizationName, [
            {
                text: invoice.organizationName,
                bold: true,
                fontSize: 15,
            },
        ]),
        printerSingleDivider(),
        ...optionalDocItem(!!invoice.orderNumber, [
            textWithBorder(`Order # ${invoice.orderNumber}`),
        ]),
        ...optionalDocItem(!!invoice.orderTitle, [
            {
                text: invoice.orderTitle,
                bold: true,
                fontSize: 14,
                marginTop: 3,
            },
        ]),
        printerSingleDivider(),
        {
            columns: [
                {
                    text: formatTime().fullDate,
                    alignment: 'left',
                    marginBottom: 5,
                },
                {
                    text: 'التاريخ:',
                    alignment: 'right',
                },
            ],
        },
        ...optionalDocItem(!!invoice.orderType, [
            {
                columns: [
                    {
                        text: invoice.orderType,
                        alignment: 'left',
                        marginBottom: 5,
                    },
                    {
                        text: 'نوع الطلب:',
                        alignment: 'right',
                    },
                ],
            },
        ]),
        ...optionalDocItem(!!invoice.table, [
            {
                columns: [
                    {
                        text: invoice.table || '',
                        alignment: 'left',
                    },
                    {
                        text: 'الطاولة:',
                        alignment: 'right',
                    },
                ],
            },
        ]),
        printerSingleDivider(),
        {
            table: {
                headerRows: 1,
                widths: ['auto', '*'],
                body: [
                    [
                        { text: 'الكمية', bold: true },
                        { text: 'المنتج', bold: true, alignment: 'right' },
                    ],
                    ...invoice.items.map((product) => {
                        const additions = product.additions?.map((a, index) => a.quantity + ' * ' + a.name + (index < product.additions.length - 1 ? " ," : "")).join(' ');

                        return [
                            { text: product.quantity, fontSize: 8 },
                            [
                                {
                                    text: product.product,
                                    alignment: 'right',
                                    fontSize: 8,
                                    decoration: product.isDeleted ? 'lineThrough' : undefined,
                                },
                                !!product.additions?.length
                                    ? {
                                        text: ' ) ' + additions + ' ( ',
                                        alignment: 'right',
                                        fontSize: 7,
                                        decoration: product.isDeleted ? 'lineThrough' : undefined
                                    }
                                    : null,
                            ],
                        ];
                    }),
                ],
            },
        },
        ...optionalDocItem(!!invoice.note, [
            printerSingleDivider(),
            {
                text: `ملاحظة: ${invoice.note}`,
                alignment: 'right',
                fontSize: 10,
            },
        ]),
    ];
};
