import { Injectable } from '@nestjs/common';
import { getAllPrinters, saveAndPrint } from '../common/helpers/printerHelper';
import { Printer } from 'pdf-to-printer';
import { PrintDto } from '../printer/dto/print.dto';
import { debug } from 'util';
import { handleKitchenReceiptContent } from '../common/printers/kitchen';
import { KitchenPrintDto } from '../printer/dto/kitchen-print.dto';
import { InvoicePrintDto } from '../printer/dto/invoice-print.dto';
import { handleInvoiceReceiptContent } from '../common/printers/invoice';
import { ShiftPrintDto } from '../printer/dto/shift-print.dto';
import { handleShiftReceiptContent } from '../common/printers/shift';
import { handleRsSvgPrint } from '../common/helpers/pdfHelper';

@Injectable()
export class PrinterService {
  async getPrinters(): Promise<Printer[]> {
    try {
      return await getAllPrinters();
    } catch (error) {
      debug('Error in getPrinters:', error);
      throw error;
    }
  }

  async print({ printerId, body }: PrintDto): Promise<void> {
    try {
      const content = handleRsSvgPrint(body);
      await saveAndPrint(printerId, content);
    } catch (error) {
      debug('Error in print:', error);
      throw error;
    }
  }

  async printKitchen({ printerId, body }: KitchenPrintDto): Promise<void> {
    try {
      const content = handleKitchenReceiptContent(body);
      await saveAndPrint(printerId, content);
    } catch (error) {
      debug('Error in printKitchen:', error);
      throw error;
    }
  }

  async printInvoice({ printerId, body }: InvoicePrintDto): Promise<void> {
    try {
      const content = handleInvoiceReceiptContent(body);
      await saveAndPrint(printerId, content);
    } catch (error) {
      debug('Error in printInvoice:', error);
      throw error;
    }
  }

  async printShift({ printerId, body }: ShiftPrintDto): Promise<void> {
    try {
      const content = handleShiftReceiptContent(body);
      await saveAndPrint(printerId, content);
    } catch (error) {
      debug('Error in printInvoice:', error);
      throw error;
    }
  }
}
