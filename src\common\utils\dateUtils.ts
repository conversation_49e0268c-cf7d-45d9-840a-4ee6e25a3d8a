export const formatTime = (date: Date = new Date()) => {
    // d-m-y h:m a
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const hours = date.getHours();
    const hours12 = hours % 12 || 12;
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";

    return {
        fullDate: `${day}-${month}-${year} - ${hours12}:${minutes} ${ampm}`,
        date: `${day}-${month}-${year}`,
        time: `${hours12}:${minutes} ${ampm}`,
    }
};