import { IInvoiceProductModel } from "./invoiceProductModel";

export interface IInvoiceModel {
    organizationName: string;
    organizationSubName?: string;
    orderType?: string;
    address?: string;
    vatNo?: string;
    crNo?: string;
    phone: string;
    invoiceTitle: string;
    invoiceNumber: string;
    orderNumber: string;
    items: IInvoiceProductModel[];
    subTotal: string;
    discount: string;
    tobaccoTax?: string;
    vat: string;
    total: string;
    nativeTotal: string;
    cash: string;
    totalDeliverApp: string;
    network: string;
    qrCode: string;
    footer?: string;
    customerName?: string;
    customerMobile?: string;
    customerAddress?: string;
    customerTaxNumber?: string;
}