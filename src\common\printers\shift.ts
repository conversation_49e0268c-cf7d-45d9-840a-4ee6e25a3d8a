import { Content } from 'pdfmake/interfaces';
import { multiText, optionalDocItem, printerSingleDivider } from '../utils/printerUtils';
import { IShiftModel } from '../models/shiftModel';
import { formatTime } from '../utils/dateUtils';

export const handleShiftReceiptContent = (invoice: IShiftModel): Content[] => {
    return [
        {
            image: "./images/logo.jpg",
            width: 150,
            height: 100,
        },
        {
            text: 'إقفال الوردية',
            bold: true,
            fontSize: 12,
            marginBottom: 5,
        },

        multiText([
            { text: ". Sales Summary", alignment: "left" },
            { text: ". ملخص المبيعات", alignment: "right" },
        ], true, 10),
        multiText([
            { text: "Invoices", alignment: "left" },
            { text: invoice.ordersCount.toString(), alignment: "center", width: "auto" },
            { text: "عدد الفواتير", alignment: "right" },
        ]),
        printerSingleDivider(),
        multiText([
            { text: "T.Discount", alignment: "left" },
            { text: invoice.discountAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "إجمالي الخصم", alignment: "right" },
        ]),
        printerSingleDivider(),
        multiText([
            { text: "T.Sales", alignment: "left" },
            { text: invoice.totalAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "إجمالي المبيعات", alignment: "right" },
        ]),
        printerSingleDivider(),
        multiText([
            { text: "Cash", alignment: "left" },
            { text: invoice.cashAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "مبيعات الكاش", alignment: "right" },
        ]),
        printerSingleDivider(),
        multiText([
            { text: "Network", alignment: "left" },
            { text: invoice.networkAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "مبيعات الشبكة", alignment: "right" },
        ]),

        invoice.paymentAmounts.map((payment) => {
            return [
                ...optionalDocItem(payment.amount > 0, [
                    printerSingleDivider(),
                    multiText([
                        { text: payment.nameEn, alignment: "left" },
                        { text: payment.amount.toFixed(2), alignment: "center", width: "auto" },
                        { text: payment.name, alignment: "right" },
                    ]),
                ])]
        }),

        multiText([
            { text: ". Accounting", alignment: "left" },
            { text: ". الحركة المالية", alignment: "right" },
        ], true, 10),
        multiText([
            { text: "Start Shift", alignment: "left" },
            { text: invoice.startAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "رصيد بداية الوردية", alignment: "right" },
        ]),
        printerSingleDivider(),
        multiText([
            { text: "Close Shift", alignment: "left" },
            { text: invoice.endAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "رصيد الإغلاق", alignment: "right" },
        ]),
        printerSingleDivider(),
        multiText([
            { text: "Addition", alignment: "left" },
            { text: invoice.additionAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "الزيادة", alignment: "right" },
        ]),
        printerSingleDivider(),
        multiText([
            { text: "Shortage", alignment: "left" },
            { text: invoice.shortageAmount.toFixed(2), alignment: "center", width: "auto" },
            { text: "العجز", alignment: "right" },
        ]),

        multiText([
            { text: ". Other", alignment: "left" },
            { text: ". أخرى", alignment: "right" },
        ], true, 10),
        multiText([
            { text: "Start Time", alignment: "left" },
            { text: formatTime(new Date(invoice.startTime)).fullDate, alignment: "center", width: "auto" },
            { text: "وقت فتح الوردية", alignment: "right" },
        ]),
        printerSingleDivider(),
        multiText([
            { text: "End Time", alignment: "left" },
            { text: formatTime(new Date(invoice.endTime)).fullDate, alignment: "center", width: "auto" },
            { text: "وقت غلق الوردية", alignment: "right" },
        ]),
    ];
};
